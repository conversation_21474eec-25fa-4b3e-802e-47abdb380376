/**
 * 项目页面
 */
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  List,
  Typography,
  Space,
  Input,
  Modal,
  Form,
  Select,
  Tabs,
  Tag,
  Tooltip,
  Empty,
  Spin,
  message} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ShareAltOutlined,
  SettingOutlined,
  FolderOpenOutlined,
  ClockCircleOutlined,
  UserOutlined,
  LockOutlined,
  UnlockOutlined,
  AppstoreOutlined,
  BarsOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store';
import {
  fetchProjects,
  createProject,
  deleteProject,
  createScene,
  setCurrentProject,
  setCurrentScene} from '../store/project/projectSlice';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Search } = Input;

export const ProjectsPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const { projects, isLoading, error } = useAppSelector((state) => state.project);
  
  const [searchValue, setSearchValue] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [newProjectModalVisible, setNewProjectModalVisible] = useState(false);
  const [newSceneModalVisible, setNewSceneModalVisible] = useState(false);
  const [deleteProjectModalVisible, setDeleteProjectModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  
  const [form] = Form.useForm();
  const [sceneForm] = Form.useForm();
  
  // 检查认证状态
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: '/projects' } });
    }
  }, [isAuthenticated, navigate]);
  
  // 加载项目列表
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchProjects());
    }
  }, [dispatch, isAuthenticated]);
  
  // 处理错误
  useEffect(() => {
    if (error) {
      message.error(error);
    }
  }, [error]);
  
  // 过滤项目
  const filteredProjects = projects.filter(
    (project) =>
      project.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      project.description.toLowerCase().includes(searchValue.toLowerCase())
  );
  
  // 创建新项目
  const handleCreateProject = () => {
    form.validateFields().then((values) => {
      dispatch(createProject(values))
        .unwrap()
        .then(() => {
          setNewProjectModalVisible(false);
          form.resetFields();
          message.success(t('projects.createSuccess'));
        })
        .catch((error) => {
          message.error(error || t('projects.createError'));
        });
    });
  };
  
  // 创建新场景
  const handleCreateScene = () => {
    if (!selectedProject) return;
    
    sceneForm.validateFields().then((values) => {
      dispatch(createScene({ projectId: selectedProject.id, ...values }))
        .unwrap()
        .then((scene) => {
          setNewSceneModalVisible(false);
          sceneForm.resetFields();
          message.success(t('projects.sceneCreateSuccess'));
          
          // 导航到编辑器
          dispatch(setCurrentProject(selectedProject));
          dispatch(setCurrentScene(scene));
          navigate(`/editor/${selectedProject.id}/${scene.id}`);
        })
        .catch((error) => {
          message.error(error || t('projects.sceneCreateError'));
        });
    });
  };
  
  // 删除项目
  const handleDeleteProject = () => {
    if (!selectedProject) return;
    
    dispatch(deleteProject(selectedProject.id))
      .unwrap()
      .then(() => {
        setDeleteProjectModalVisible(false);
        setSelectedProject(null);
        message.success(t('projects.deleteSuccess'));
      })
      .catch((error) => {
        message.error(error || t('projects.deleteError'));
      });
  };
  
  // 打开项目
  const handleOpenProject = (project: any) => {
    dispatch(setCurrentProject(project));
    
    // 如果项目有场景，打开第一个场景
    if (project.scenes && project.scenes.length > 0) {
      const scene = project.scenes[0];
      dispatch(setCurrentScene(scene));
      navigate(`/editor/${project.id}/${scene.id}`);
    } else {
      // 否则打开新建场景对话框
      setSelectedProject(project);
      setNewSceneModalVisible(true);
    }
  };
  
  // 渲染网格视图
  const renderGridView = () => {
    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 3, xl: 4, xxl: 6 }}
        dataSource={filteredProjects}
        renderItem={(project) => (
          <List.Item>
            <Card
              hoverable
              cover={
                project.thumbnail ? (
                  <img alt={project.name} src={project.thumbnail} style={{ height: 160, objectFit: 'cover' }} />
                ) : (
                  <div
                    style={{
                      height: 160,
                      background: '#f0f0f0',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center'}}
                  >
                    <FolderOpenOutlined style={{ fontSize: 48, color: '#999' }} />
                  </div>
                )
              }
              actions={[
                <Tooltip title={t('projects.open')}>
                  <Button type="text" icon={<FolderOpenOutlined />} onClick={() => handleOpenProject(project)} />
                </Tooltip>,
                <Tooltip title={t('projects.edit')}>
                  <Button type="text" icon={<EditOutlined />} />
                </Tooltip>,
                <Tooltip title={t('projects.delete')}>
                  <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    onClick={() => {
                      setSelectedProject(project);
                      setDeleteProjectModalVisible(true);
                    }}
                  />
                </Tooltip>,
              ]}
            >
              <Card.Meta
                title={project.name}
                description={
                  <>
                    <div style={{ marginBottom: 8 }}>{project.description}</div>
                    <div>
                      <Space>
                        <Tag icon={<ClockCircleOutlined />}>
                          {new Date(project.updatedAt).toLocaleDateString()}
                        </Tag>
                        <Tag icon={project.isPublic ? <UnlockOutlined /> : <LockOutlined />}>
                          {project.isPublic ? t('projects.public') : t('projects.private')}
                        </Tag>
                      </Space>
                    </div>
                  </>
                }
              />
            </Card>
          </List.Item>
        )}
      />
    );
  };
  
  // 渲染列表视图
  const renderListView = () => {
    return (
      <List
        itemLayout="horizontal"
        dataSource={filteredProjects}
        renderItem={(project) => (
          <List.Item
            actions={[
              <Button type="link" onClick={() => handleOpenProject(project)}>
                {t('projects.open')}
              </Button>,
              <Button type="text" icon={<EditOutlined />} />,
              <Button
                type="text"
                icon={<DeleteOutlined />}
                onClick={() => {
                  setSelectedProject(project);
                  setDeleteProjectModalVisible(true);
                }}
              />,
            ]}
          >
            <List.Item.Meta
              avatar={
                project.thumbnail ? (
                  <img
                    src={project.thumbnail}
                    alt={project.name}
                    style={{ width: 48, height: 48, objectFit: 'cover', borderRadius: 4 }}
                  />
                ) : (
                  <div
                    style={{
                      width: 48,
                      height: 48,
                      background: '#f0f0f0',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 4}}
                  >
                    <FolderOpenOutlined style={{ fontSize: 24, color: '#999' }} />
                  </div>
                )
              }
              title={project.name}
              description={
                <>
                  <div>{project.description}</div>
                  <div style={{ marginTop: 4 }}>
                    <Space>
                      <Tag icon={<ClockCircleOutlined />}>
                        {new Date(project.updatedAt).toLocaleDateString()}
                      </Tag>
                      <Tag icon={project.isPublic ? <UnlockOutlined /> : <LockOutlined />}>
                        {project.isPublic ? t('projects.public') : t('projects.private')}
                      </Tag>
                    </Space>
                  </div>
                </>
              }
            />
          </List.Item>
        )}
      />
    );
  };
  
  return (
    <div style={{ padding: 24 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 24 }}>
        <Title level={2}>{t('projects.title')}</Title>
        <Space>
          <Search
            placeholder={t('projects.search') as string}
            allowClear
            onChange={(e) => setSearchValue(e.target.value)}
            style={{ width: 250 }}
          />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setNewProjectModalVisible(true)}>
            {t('projects.new')}
          </Button>
          <Tooltip title={t('projects.gridView')}>
            <Button
              type={viewMode === 'grid' ? 'primary' : 'default'}
              icon={<AppstoreOutlined />}
              onClick={() => setViewMode('grid')}
            />
          </Tooltip>
          <Tooltip title={t('projects.listView')}>
            <Button
              type={viewMode === 'list' ? 'primary' : 'default'}
              icon={<BarsOutlined />}
              onClick={() => setViewMode('list')}
            />
          </Tooltip>
        </Space>
      </div>
      
      <Tabs defaultActiveKey="all">
        <TabPane tab={t('projects.allProjects')} key="all">
          {isLoading ? (
            <div style={{ display: 'flex', justifyContent: 'center', padding: 40 }}>
              <Spin size="large" />
            </div>
          ) : filteredProjects.length > 0 ? (
            viewMode === 'grid' ? renderGridView() : renderListView()
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                searchValue ? t('projects.noSearchResults') : t('projects.noProjects')
              }
            >
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setNewProjectModalVisible(true)}>
                {t('projects.createFirst')}
              </Button>
            </Empty>
          )}
        </TabPane>
        <TabPane tab={t('projects.myProjects')} key="my">
          <Empty description={t('projects.noMyProjects')} />
        </TabPane>
        <TabPane tab={t('projects.shared')} key="shared">
          <Empty description={t('projects.noSharedProjects')} />
        </TabPane>
      </Tabs>
      
      {/* 新建项目对话框 */}
      <Modal
        title={t('projects.newProject')}
        open={newProjectModalVisible}
        onOk={handleCreateProject}
        onCancel={() => setNewProjectModalVisible(false)}
        okText={t('common.create')}
        cancelText={t('common.cancel')}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label={t('projects.name')}
            rules={[{ required: true, message: t('projects.nameRequired') as string }]}
          >
            <Input placeholder={t('projects.namePlaceholder') as string} />
          </Form.Item>
          <Form.Item name="description" label={t('projects.description')}>
            <Input.TextArea rows={4} placeholder={t('projects.descriptionPlaceholder') as string} />
          </Form.Item>
          <Form.Item name="isPublic" label={t('projects.visibility')} initialValue={false}>
            <Select>
              <Option value={false}>{t('projects.private')}</Option>
              <Option value={true}>{t('projects.public')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 新建场景对话框 */}
      <Modal
        title={t('projects.newScene')}
        open={newSceneModalVisible}
        onOk={handleCreateScene}
        onCancel={() => setNewSceneModalVisible(false)}
        okText={t('common.create')}
        cancelText={t('common.cancel')}
      >
        <Form form={sceneForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('projects.sceneName')}
            rules={[{ required: true, message: t('projects.sceneNameRequired') as string }]}
          >
            <Input placeholder={t('projects.sceneNamePlaceholder') as string} />
          </Form.Item>
          <Form.Item name="description" label={t('projects.sceneDescription')}>
            <Input.TextArea rows={4} placeholder={t('projects.sceneDescriptionPlaceholder') as string} />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 删除项目确认对话框 */}
      <Modal
        title={t('projects.deleteConfirmTitle')}
        open={deleteProjectModalVisible}
        onOk={handleDeleteProject}
        onCancel={() => setDeleteProjectModalVisible(false)}
        okText={t('common.delete')}
        cancelText={t('common.cancel')}
        okButtonProps={{ danger: true }}
      >
        <p>
          {t('projects.deleteConfirmMessage', {
            name: selectedProject?.name})}
        </p>
        <p>{t('projects.deleteConfirmWarning')}</p>
      </Modal>
    </div>
  );
};
