/**
 * 动画编辑器
 */
import React, { useEffect } from 'react';
import { Card, Form, InputNumber, Typography, Select, Switch, Button, Space } from 'antd';

const { Text } = Typography;

/**
 * 动画编辑器属性
 */
interface AnimationEditorProps {
  /** 动画ID */
  animationId?: string;
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
  /** 保存回调 */
  onSave?: (data: any) => void;
  /** 取消回调 */
  onCancel?: () => void;
}

/**
 * 动画编辑器组件
 */
const AnimationEditor: React.FC<AnimationEditorProps> = ({
  animationId,
  data,
  onChange,
  onSave,
  onCancel
}) => {
  const [form] = Form.useForm();

  // 默认数据
  const defaultData = {
    enabled: true,
    autoPlay: false,
    loop: true,
    speed: 1.0,
    ...data
  };

  // 处理数据变化
  const handleChange = (field: string, value: any) => {
    const newData = { ...defaultData, [field]: value };
    if (onChange) {
      onChange(newData);
    }
  };

  // 处理保存
  const handleSave = () => {
    if (onSave) {
      onSave(defaultData);
    }
  };

  // 处理取消
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // 根据animationId加载动画数据
  useEffect(() => {
    if (animationId) {
      // 这里应该根据animationId加载动画数据
      console.log('Loading animation:', animationId);
    }
  }, [animationId]);

  return (
    <Card title="动画" size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultData}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleChange(field, value);
          });
        }}
      >
        <Form.Item label="启用" name="enabled" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="自动播放" name="autoPlay" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="循环播放" name="loop" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="播放速度" name="speed">
          <InputNumber min={0.1} max={5} step={0.1} />
        </Form.Item>

        {(onSave || onCancel) && (
          <Form.Item>
            <Space>
              {onSave && (
                <Button type="primary" onClick={handleSave}>
                  保存
                </Button>
              )}
              {onCancel && (
                <Button onClick={handleCancel}>
                  取消
                </Button>
              )}
            </Space>
          </Form.Item>
        )}
      </Form>
    </Card>
  );
};

export default AnimationEditor;
